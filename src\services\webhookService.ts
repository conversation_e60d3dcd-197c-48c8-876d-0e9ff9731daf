import * as fs from 'fs';
import * as path from 'path';
import WebhookLog from '../services/models/WebhookLog';
import logger from '../helpers/logger';

export interface IWebhookPayload {
  [key: string]: any;
}

export interface IWebhookProcessingResult {
  success: boolean;
  message: string;
  loggedToFile?: boolean;
  loggedToDatabase?: boolean;
}

export class WebhookService {
  private static instance: WebhookService;
  private logFilePath: string;

  public static getInstance(): WebhookService {
    if (!WebhookService.instance) {
      WebhookService.instance = new WebhookService();
    }
    return WebhookService.instance;
  }

  constructor() {
    this.logFilePath = path.join(__dirname, '../../logs/webhook-responses.log');
    this.ensureLogDirectory();
  }

  private ensureLogDirectory(): void {
    const dir = path.dirname(this.logFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      logger.info('Created logs directory for webhooks', { path: dir });
    }
  }

  /**
   * Process incoming webhook payload
   */
  public async processWebhook(payload: IWebhookPayload): Promise<IWebhookProcessingResult> {
    try {
      logger.info('Processing webhook payload', {
        payloadSize: JSON.stringify(payload).length,
        hasShipper: !!payload.shipper,
        hasConsignee: !!payload.consignee
      });

      const result: IWebhookProcessingResult = {
        success: true,
        message: 'Webhook processed successfully'
      };

      // Log to file asynchronously
      try {
        await this.logToFile(payload);
        result.loggedToFile = true;
        logger.debug('Webhook logged to file successfully');
      } catch (error) {
        logger.error('Failed to log webhook to file', error);
        result.loggedToFile = false;
      }

      // Log to database asynchronously
      try {
        await this.logToDatabase(payload);
        result.loggedToDatabase = true;
        logger.debug('Webhook logged to database successfully');
      } catch (error) {
        logger.error('Failed to log webhook to database', error);
        result.loggedToDatabase = false;
      }

      return result;
    } catch (error) {
      logger.error('Webhook processing failed', error);
      return {
        success: false,
        message: 'Failed to process webhook'
      };
    }
  }

  /**
   * Log webhook payload to file
   */
  private async logToFile(payload: IWebhookPayload): Promise<void> {
    const timestamp = new Date().toISOString();
    const logEntry = `\n[${timestamp}] ${JSON.stringify(payload, null, 2)}\n`;

    await fs.promises.appendFile(this.logFilePath, logEntry);
  }

  /**
   * Log webhook payload to database
   */
  private async logToDatabase(payload: IWebhookPayload): Promise<void> {
    await WebhookLog.create({
      payload,
      receivedAt: new Date()
    });
  }

  /**
   * Get webhook logs with pagination
   */
  public async getWebhookLogs(
    page: number = 1,
    limit: number = 10,
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    logs: any[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    try {
      const query: any = {};

      if (startDate || endDate) {
        query.receivedAt = {};
        if (startDate) query.receivedAt.$gte = startDate;
        if (endDate) query.receivedAt.$lte = endDate;
      }

      const skip = (page - 1) * limit;

      const [logs, total] = await Promise.all([
        WebhookLog.find(query)
          .sort({ receivedAt: -1 })
          .skip(skip)
          .limit(limit)
          .lean(),
        WebhookLog.countDocuments(query)
      ]);

      return {
        logs,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Failed to retrieve webhook logs', error);
      throw error;
    }
  }

  /**
   * Get webhook statistics
   */
  public async getWebhookStats(
    startDate?: Date,
    endDate?: Date
  ): Promise<{
    totalWebhooks: number;
    successfulLogs: number;
    failedLogs: number;
    averagePayloadSize: number;
  }> {
    try {
      const query: any = {};

      if (startDate || endDate) {
        query.receivedAt = {};
        if (startDate) query.receivedAt.$gte = startDate;
        if (endDate) query.receivedAt.$lte = endDate;
      }

      const logs = await WebhookLog.find(query).lean();

      const totalWebhooks = logs.length;
      const successfulLogs = logs.length; // All logs in DB are successful
      const failedLogs = 0; // This could be enhanced with error tracking
      const averagePayloadSize = logs.length > 0
        ? logs.reduce((sum: number, log: any) => sum + JSON.stringify(log.payload).length, 0) / logs.length
        : 0;

      return {
        totalWebhooks,
        successfulLogs,
        failedLogs,
        averagePayloadSize: Math.round(averagePayloadSize)
      };
    } catch (error) {
      logger.error('Failed to get webhook statistics', error);
      throw error;
    }
  }
}

export const webhookService = WebhookService.getInstance();
