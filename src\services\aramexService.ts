import config from "../config/appConfig";
import { ApiError } from "../helpers/apiResponse";
import { addDays, formatDateForApi, httpClient } from "../helpers/httpClient";
import logger from "../helpers/logger";

export interface IAramexRateRequest {
  OriginAddress: {
    Line1: string;
    City: string;
    CountryCode: string;
    [key: string]: any;
  };
  DestinationAddress: {
    Line1: string;
    City: string;
    CountryCode: string;
    [key: string]: any;
  };
  ShipmentDetails: {
    ActualWeight: number;
    ChargeableWeight: number;
    [key: string]: any;
  };
  PreferredCurrencyCode: string;
}

export interface IAramexShipment {
  Reference1: string;
  Reference2?: string;
  Reference3?: string;
  Shipper: any;
  Consignee: any;
  ThirdParty: any;
  ShippingDateTime: string;
  DueDate: string;
  Comments?: string | null;
  PickupLocation?: string | null;
  OperationsInstructions?: string | null;
  AccountingInstrcutions?: string | null;
  Details: any;
  TransportType?: number;
}

export interface IAramexRateResponse {
  success: boolean;
  data?: any;
  message: string;
}

export interface IAramexShipmentResponse {
  success: boolean;
  data?: any;
  message: string;
  trackingNumber?: string;
  reference?: string;
}

export class AramexService {
  private static instance: AramexService;

  public static getInstance(): AramexService {
    if (!AramexService.instance) {
      AramexService.instance = new AramexService();
    }
    return AramexService.instance;
  }

  /**
   * Calculate shipping rate using Aramex API
   */
  public async calculateRate(
    rateRequest: IAramexRateRequest
  ): Promise<IAramexRateResponse> {
    try {
      logger.info("Calculating shipping rate", {
        origin: rateRequest.OriginAddress.City,
        destination: rateRequest.DestinationAddress.City,
        weight: rateRequest.ShipmentDetails.ActualWeight,
      });

      const payload = {
        OriginAddress: rateRequest.OriginAddress,
        DestinationAddress: rateRequest.DestinationAddress,
        ShipmentDetails: {
          Dimensions: null,
          ActualWeight: rateRequest.ShipmentDetails.ActualWeight,
          ChargeableWeight: rateRequest.ShipmentDetails.ChargeableWeight,
          DescriptionOfGoods: null,
          GoodsOriginCountry: null,
          NumberOfPieces: 1,
          ProductGroup: "DOM",
          ProductType: "ONP",
          PaymentType: "P",
          PaymentOptions: null,
          CustomsValueAmount: null,
          CashOnDeliveryAmount: null,
          InsuranceAmount: null,
          CashAdditionalAmount: null,
          CashAdditionalAmountDescription: null,
          CollectAmount: null,
          Services: "",
          Items: null,
          DeliveryInstructions: null,
          AdditionalProperties: null,
          ContainsDangerousGoods: false,
        },
        PreferredCurrencyCode: rateRequest.PreferredCurrencyCode,
        ClientInfo: config.aramex.clientInfo,
        Transaction: null,
      };

      const endpoint = `${config.aramex.baseUrl}/ShippingAPI.V2/RateCalculator/Service_1_0.svc/json/CalculateRate`;

      const headers = {
        "content-type": "application/json",
        Accept: "application/json",
        Authorization: `Basic ${Buffer.from(
          `${config.aramex.adminUserName}:${config.aramex.adminPassword}`
        ).toString("base64")}`,
      };

      const response = await httpClient.request({
        method: "POST",
        url: endpoint,
        headers,
        data: payload,
        timeout: 30000,
      });

      if (response.status >= 200 && response.status < 300) {
        logger.info("Rate calculation successful", {
          status: response.status,
          responseSize: JSON.stringify(response.data).length,
        });

        return {
          success: true,
          data: response.data,
          message: "Rate calculated successfully",
        };
      } else {
        logger.error("Aramex rate calculation failed", {
          status: response.status,
          statusText: response.statusText,
          response: response.data,
        });

        return {
          success: false,
          message: `Aramex API Error: ${
            response.statusText || "Failed to calculate rate"
          }`,
        };
      }
    } catch (error: any) {
      logger.error("Rate calculation error", error);
      throw new ApiError("Failed to calculate shipping rate", 500);
    }
  }

  /**
   * Create shipment using Aramex API
   */
  public async createShipment(
    shipments: IAramexShipment[]
  ): Promise<IAramexShipmentResponse> {
    try {
      logger.info("Creating shipment", {
        shipmentCount: shipments.length,
        reference: shipments[0]?.Reference1,
      });

      const payload = {
        Shipments: shipments,
        ClientInfo: config.aramex.clientInfo,
      };

      const endpoint = `${config.aramex.baseUrl}/ShippingAPI.V2/Shipping/Service_1_0.svc/json/CreateShipments`;

      const headers = {
        "content-type": "application/json",
        Accept: "application/json",
        Authorization: `Basic ${Buffer.from(
          `${config.aramex.adminUserName}:${config.aramex.adminPassword}`
        ).toString("base64")}`,
      };

      const response = await httpClient.request({
        method: "POST",
        url: endpoint,
        headers,
        data: payload,
        timeout: 60000, // Longer timeout for shipment creation
      });

      if (response.status >= 200 && response.status < 300) {
        const trackingNumber = response.data?.Shipments?.[0]?.ID;
        const reference = response.data?.Shipments?.[0]?.Reference1;

        logger.info("Shipment created successfully", {
          status: response.status,
          trackingNumber,
          reference,
          responseSize: JSON.stringify(response.data).length,
        });

        return {
          success: true,
          data: response.data,
          message: "Shipment created successfully",
          trackingNumber,
          reference,
        };
      } else {
        logger.error("Aramex shipment creation failed", {
          status: response.status,
          statusText: response.statusText,
          response: response.data,
        });

        return {
          success: false,
          message: `Aramex API Error: ${
            response.statusText || "Failed to create shipment"
          }`,
        };
      }
    } catch (error: any) {
      logger.error("Shipment creation error", error);
      throw new ApiError("Failed to create shipment", 500);
    }
  }

  /**
   * Build Aramex shipment object from application data
   */
  public buildAramexShipment(
    shipper: any,
    consignee: any,
    thirdParty: any,
    shipmentDetails: any,
    references?: any
  ): IAramexShipment {
    return {
      Reference1: references?.reference1 || `REF-${Date.now()}`,
      Reference2: references?.reference2,
      Reference3: references?.reference3,
      Shipper: shipper,
      Consignee: consignee,
      ThirdParty: thirdParty,
      ShippingDateTime: formatDateForApi(addDays(new Date(), 1)),
      DueDate: formatDateForApi(addDays(new Date(), 2)),
      Comments: null,
      PickupLocation: null,
      OperationsInstructions: null,
      AccountingInstrcutions: null,
      Details: {
        Dimensions: {
          Length: shipmentDetails.dimensions.length,
          Width: shipmentDetails.dimensions.width,
          Height: shipmentDetails.dimensions.height,
          Unit: "CM",
        },
        ActualWeight: {
          Unit: "KG",
          Value: shipmentDetails.weight,
        },
        ChargeableWeight: {
          Unit: "KG",
          Value: Math.max(
            shipmentDetails.weight,
            (shipmentDetails.dimensions.length *
              shipmentDetails.dimensions.width *
              shipmentDetails.dimensions.height) /
              5000
          ),
        },
        NumberOfPieces: shipmentDetails.numberOfPieces,
        DescriptionOfGoods: "Items",
        GoodsOriginCountry: "AE",
        ProductGroup: "DOM",
        ProductType: "ONP",
        PaymentType: "3",
        PaymentOptions: null,
        DeliveryInstructions: null,
        ContainsDangerousGoods: false,
      },
      TransportType: 0,
    };
  }
}

export const aramexService = AramexService.getInstance();
