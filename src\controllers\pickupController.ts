import { Request, Response } from 'express';
import { as<PERSON><PERSON><PERSON><PERSON>, sendError, sendSuccess } from '../helpers/apiResponse';
import logger from '../helpers/logger';
import {
  validateAddress,
  validateAgainstSchema,
  validateContact,
  validateNumber,
  validateObject,
  validateString,
  ValidationSchema
} from '../helpers/validation';
import { sanitizeRequestBody } from '../middleware/requestLogger';
import { IPickupRequest, pickupService } from '../services/shippingService';

// ==========================================
// PICKUP VALIDATION SCHEMA
// ==========================================

const pickupAddressSchema: ValidationSchema = {
  addressLine1: { validator: validateString, required: true },
  addressLine2: { validator: validateString, required: false },
  addressLine3: { validator: validateString, required: false },
  city: { validator: validateString, required: true },
  stateOrProvinceCode: { validator: validateString, required: false },
  postCode: { validator: validateString, required: false },
  countryCode: { validator: validateString, required: true }
};

const pickupContactSchema: ValidationSchema = {
  department: { validator: validateString, required: false },
  personName: { validator: validateString, required: true },
  title: { validator: validateString, required: false },
  companyName: { validator: validateString, required: true },
  phoneNumber: { validator: validateString, required: true },
  phoneNumberExt: { validator: validateString, required: false },
  cellPhone: { validator: validateString, required: false },
  emailAddress: { validator: validateString, required: true }
};

const pickupItemSchema: ValidationSchema = {
  productGroup: { validator: validateString, required: true },
  productType: { validator: validateString, required: true },
  numberOfShipments: { validator: validateNumber, required: true },
  packageType: { validator: validateString, required: true },
  payment: { validator: validateString, required: true },
  shipmentWeight: { 
    validator: validateObject, 
    required: true,
    schema: {
      unit: { validator: validateString, required: true },
      value: { validator: validateNumber, required: true }
    }
  },
  shipmentVolume: { validator: validateObject, required: false },
  numberOfPieces: { validator: validateNumber, required: true },
  cashAmount: { validator: validateNumber, required: false },
  extraCharges: { validator: validateObject, required: false },
  shipmentDimensions: {
    validator: validateObject,
    required: true,
    schema: {
      length: { validator: validateNumber, required: true },
      width: { validator: validateNumber, required: true },
      height: { validator: validateNumber, required: true },
      unit: { validator: validateString, required: false }
    }
  },
  comments: { validator: validateString, required: false }
};

const createPickupSchema: ValidationSchema = {
  pickupAddress: { 
    validator: validateAddress, 
    required: true,
    schema: pickupAddressSchema
  },
  pickupContact: { 
    validator: validateContact, 
    required: true,
    schema: pickupContactSchema
  },
  pickupLocation: { validator: validateString, required: true },
  pickupDate: { validator: validateString, required: true },
  readyTime: { validator: validateString, required: true },
  lastPickupTime: { validator: validateString, required: true },
  closingTime: { validator: validateString, required: true },
  comments: { validator: validateString, required: false },
  vehicle: { validator: validateString, required: false },
  pickupItems: { 
    validator: (value: any) => Array.isArray(value) && value.length > 0, 
    required: true,
    schema: pickupItemSchema
  },
  references: {
    validator: validateObject,
    required: false,
    schema: {
      reference1: { validator: validateString, required: false },
      reference2: { validator: validateString, required: false }
    }
  }
};

export class PickupController {
  /**
   * Create a new pickup
   */
  public createPickup = asyncHandler(async (req: Request, res: Response) => {
    logger.info('Create pickup request received', {
      method: req.method,
      path: req.path,
      query: req.query,
      params: req.params,
      body: sanitizeRequestBody(req.body), // Log sanitized request data
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: (req as any).requestId
    });

    // Validate request
    validateAgainstSchema(req.body, createPickupSchema);

    // Log validation success
    logger.info('Request validation passed', {
      requestId: (req as any).requestId,
      validationFields: Object.keys(createPickupSchema)
    });

    // Additional business validation
    const validationResult = pickupService.validatePickupData(req.body as IPickupRequest);
    if (!validationResult.isValid) {
      logger.warn('Pickup data validation failed', {
        requestId: (req as any).requestId,
        errors: validationResult.errors
      });
      return sendError(res, 'Validation failed', 400, validationResult.errors);
    }

    try {
      // Create pickup
      const result = await pickupService.createPickup(req.body as IPickupRequest);

      if (result.success) {
        logger.info('Pickup created successfully', {
          requestId: (req as any).requestId,
          pickupGUID: result.pickupGUID,
          reference: result.reference
        });

        sendSuccess(res, result.message, {
          pickupGUID: result.pickupGUID,
          reference: result.reference,
          data: result.data
        });
      } else {
        logger.error('Pickup creation failed', {
          requestId: (req as any).requestId,
          error: result.message
        });
        sendError(res, result.message, 400);
      }
    } catch (error) {
      logger.error('Pickup controller error', {
        requestId: (req as any).requestId,
        error: error
      });
      sendError(res, 'Internal server error during pickup creation', 500);
    }
  });
}

export const pickupController = new PickupController();
