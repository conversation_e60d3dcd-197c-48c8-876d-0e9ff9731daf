import config from '../config/appConfig';
import { ApiError } from '../helpers/apiResponse';
import { generateId } from '../helpers/httpClient';
import logger from '../helpers/logger';
import { aramexService } from './aramexService';

export interface IShippingRequest {
  shipper: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateOrProvinceCode?: string;
    postCode: string;
    countryCode: string;
    personName: string;
    phoneNumber: string;
    cellPhone?: string;
    emailAddress: string;
    companyName: string;
  };
  consignee: {
    addressLine1: string;
    addressLine2?: string;
    city: string;
    stateOrProvinceCode?: string;
    postCode: string;
    countryCode: string;
    personName: string;
    phoneNumber: string;
    cellPhone?: string;
    emailAddress: string;
    companyName: string;
  };
  shipmentDetails: {
    weight: number;
    dimensions: {
      length: number;
      width: number;
      height: number;
    };
    numberOfPieces: number;
    internalOrderId?: string;
    customerCharges?: number;
  };
  references?: {
    reference1?: string;
    reference2?: string;
    reference3?: string;
  };
}

export interface IParty {
  Reference1: string | null;
  Reference2: string | null;
  AccountNumber: string;
  PartyAddress: {
    Line1: string;
    Line2: string | null;
    Line3: string;
    City: string;
    StateOrProvinceCode: string | null;
    PostCode: string;
    CountryCode: string;
    Longitude: number;
    Latitude: number;
    BuildingNumber: string | null;
    BuildingName: string | null;
    Floor: string | null;
    Apartment: string | null;
    POBox: string | null;
    Description: string | null;
  };
  Contact: {
    Department: string | null;
    PersonName: string;
    Title: string | null;
    CompanyName: string;
    PhoneNumber1: string;
    PhoneNumber1Ext: string;
    PhoneNumber2: string;
    FaxNumber: string | null;
    CellPhone: string | null;
    EmailAddress: string;
    Type: string;
  };
}

export interface IShippingResponse {
  success: boolean;
  message: string;
  data?: any;
  trackingNumber?: string;
  reference?: string;
}

export class ShippingService {
  private static instance: ShippingService;

  public static getInstance(): ShippingService {
    if (!ShippingService.instance) {
      ShippingService.instance = new ShippingService();
    }
    return ShippingService.instance;
  }

  /**
   * Create a new shipment
   */
  public async createShipment(request: IShippingRequest): Promise<IShippingResponse> {
    try {
      logger.info('Creating new shipment', {
        shipper: request.shipper.companyName,
        consignee: request.consignee.companyName,
        weight: request.shipmentDetails.weight,
        pieces: request.shipmentDetails.numberOfPieces
      });

      // Build party objects
      const shipper = this.buildParty(request.shipper, true);
      const consignee = this.buildParty(request.consignee, false);
      const thirdParty = this.buildThirdParty(shipper, request);

      // Build Aramex shipment
      const aramexShipment = aramexService.buildAramexShipment(
        shipper,
        consignee,
        thirdParty,
        request.shipmentDetails,
        request.references
      );

      // Create shipment via Aramex
      const result = await aramexService.createShipment([aramexShipment]);

      if (result.success) {
        logger.info('Shipment created successfully', {
          reference: result.reference,
          trackingNumber: result.trackingNumber
        });

        return {
          success: true,
          message: 'Shipment created successfully',
          data: result.data,
          trackingNumber: result.trackingNumber,
          reference: result.reference
        };
      } else {
        logger.error('Shipment creation failed', { error: result.message });
        return {
          success: false,
          message: result.message || 'Failed to create shipment'
        };
      }
    } catch (error) {
      logger.error('Shipping service error', error);
      throw new ApiError('Failed to create shipment', 500);
    }
  }

  /**
   * Build party object from request data
   */
  private buildParty(data: any, isShipper: boolean = true): IParty {
    return {
      Reference1: data.references?.reference1 || null,
      Reference2: data.references?.reference2 || null,
      AccountNumber: '',
      PartyAddress: {
        Line1: data.addressLine1,
        Line2: data.addressLine2 || null,
        Line3: data.addressLine3 || null,
        City: data.city,
        StateOrProvinceCode: data.stateOrProvinceCode || null,
        PostCode: data.postCode,
        CountryCode: data.countryCode,
        Longitude: 0,
        Latitude: 0,
        BuildingNumber: null,
        BuildingName: null,
        Floor: null,
        Apartment: null,
        POBox: null,
        Description: null,
      },
      Contact: {
        Department: null,
        PersonName: data.personName,
        Title: null,
        CompanyName: data.companyName,
        PhoneNumber1: data.phoneNumber,
        PhoneNumber1Ext: "",
        PhoneNumber2: "",
        FaxNumber: null,
        CellPhone: data.cellPhone || data.phoneNumber,
        EmailAddress: data.emailAddress,
        Type: "",
      },
    };
  }

  /**
   * Build third party object for billing
   */
  private buildThirdParty(shipper: IParty, request: IShippingRequest): IParty {
    return {
      Reference1: request.references?.reference1 || `Internal Order: ${request.shipmentDetails.internalOrderId || generateId('ORD')}`,
      Reference2: request.references?.reference2 || `Customer Charges: ${request.shipmentDetails.customerCharges || 0}`,
      AccountNumber: config.aramex.adminAccountNumber,
      PartyAddress: {
        Line1: shipper.PartyAddress.Line1,
        Line2: shipper.PartyAddress.Line2,
        Line3: shipper.PartyAddress.Line3,
        City: shipper.PartyAddress.City,
        StateOrProvinceCode: shipper.PartyAddress.StateOrProvinceCode,
        PostCode: shipper.PartyAddress.PostCode,
        CountryCode: shipper.PartyAddress.CountryCode,
        Longitude: 0,
        Latitude: 0,
        BuildingNumber: null,
        BuildingName: null,
        Floor: null,
        Apartment: null,
        POBox: null,
        Description: null,
      },
      Contact: {
        Department: "Billing Department",
        PersonName: "App Admin",
        Title: "Billing Manager",
        CompanyName: "Your Company Name",
        PhoneNumber1: "ADMIN_PHONE", // Use config
        PhoneNumber1Ext: "",
        PhoneNumber2: "",
        FaxNumber: null,
        CellPhone: "*********",
        EmailAddress: "ADMIN_EMAIL", // Use config
        Type: "",
      },
    };
  }

  /**
   * Calculate shipment cost estimate
   */
  public async calculateShippingCost(request: IShippingRequest): Promise<{
    success: boolean;
    cost?: number;
    currency?: string;
    message: string;
  }> {
    try {
      // This would typically call Aramex rate calculator
      // For now, return a placeholder
      logger.info('Calculating shipping cost', {
        origin: request.shipper.city,
        destination: request.consignee.city,
        weight: request.shipmentDetails.weight
      });

      // Placeholder calculation - replace with actual Aramex rate calculation
      const baseRate = 50; // Base rate
      const weightMultiplier = request.shipmentDetails.weight * 2;
      const distanceMultiplier = 1.5; // Based on distance
      const estimatedCost = (baseRate + weightMultiplier) * distanceMultiplier;

      return {
        success: true,
        cost: Math.round(estimatedCost * 100) / 100,
        currency: 'AED',
        message: 'Cost calculated successfully'
      };
    } catch (error) {
      logger.error('Cost calculation error', error);
      return {
        success: false,
        message: 'Failed to calculate shipping cost'
      };
    }
  }

  /**
   * Validate shipment data
   */
  public validateShipmentData(data: IShippingRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate shipper
    if (!data.shipper?.addressLine1) errors.push('Shipper address line 1 is required');
    if (!data.shipper?.city) errors.push('Shipper city is required');
    if (!data.shipper?.countryCode) errors.push('Shipper country code is required');
    if (!data.shipper?.personName) errors.push('Shipper person name is required');
    if (!data.shipper?.phoneNumber) errors.push('Shipper phone number is required');

    // Validate consignee
    if (!data.consignee?.addressLine1) errors.push('Consignee address line 1 is required');
    if (!data.consignee?.city) errors.push('Consignee city is required');
    if (!data.consignee?.countryCode) errors.push('Consignee country code is required');
    if (!data.consignee?.personName) errors.push('Consignee person name is required');
    if (!data.consignee?.phoneNumber) errors.push('Consignee phone number is required');

    // Validate shipment details
    if (!data.shipmentDetails?.weight || data.shipmentDetails.weight <= 0) {
      errors.push('Valid shipment weight is required');
    }
    if (!data.shipmentDetails?.dimensions?.length || data.shipmentDetails.dimensions.length <= 0) {
      errors.push('Valid shipment length is required');
    }
    if (!data.shipmentDetails?.dimensions?.width || data.shipmentDetails.dimensions.width <= 0) {
      errors.push('Valid shipment width is required');
    }
    if (!data.shipmentDetails?.dimensions?.height || data.shipmentDetails.dimensions.height <= 0) {
      errors.push('Valid shipment height is required');
    }
    if (!data.shipmentDetails?.numberOfPieces || data.shipmentDetails.numberOfPieces <= 0) {
      errors.push('Valid number of pieces is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

export const shippingService = ShippingService.getInstance();
