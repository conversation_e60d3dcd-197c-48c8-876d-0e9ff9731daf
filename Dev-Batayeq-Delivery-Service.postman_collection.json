{"info": {"_postman_id": "a42858a3-aa13-486b-bfc4-c136dcfbad8a", "name": "Batayeq-Delivery-Service", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "48045535", "_collection_link": "https://anik<PERSON><PERSON>-v-dev-3296891.postman.co/workspace/<PERSON><PERSON><PERSON><PERSON>'s-Workspace~43dd5d62-7ec1-42ae-8f13-de166e438d52/collection/48045535-a42858a3-aa13-486b-bfc4-c136dcfbad8a?action=share&source=collection_link&creator=48045535"}, "item": [{"name": "Create-Shipment", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"shipper\": {\r\n        \"companyName\": \"ABC Company\",\r\n        \"personName\": \"<PERSON>\",\r\n        \"phoneNumber\": \"9092343945\",\r\n        \"cellPhone\": \"971556893100\",\r\n        \"emailAddress\": \"<EMAIL>\",\r\n        \"addressLine1\": \"123 Business Street\",\r\n        \"addressLine2\": \"Suite 100\",\r\n        \"city\": \"Dubai\",\r\n        \"postCode\": \"00000\",\r\n        \"countryCode\": \"AE\"\r\n    },\r\n    \"consignee\": {\r\n        \"companyName\": \"XYZ Corp\",\r\n        \"personName\": \"<PERSON>\",\r\n        \"phoneNumber\": \"9093243837\",\r\n        \"emailAddress\": \"<EMAIL>\",\r\n        \"addressLine1\": \"456 Delivery Ave\",\r\n        \"city\": \"Abu Dhabi\",\r\n        \"postCode\": \"12345\",\r\n        \"countryCode\": \"AE\"\r\n    },\r\n    \"shipmentDetails\": {\r\n        \"numberOfPieces\": 1,\r\n        \"weight\": 0.5,\r\n        \"dimensions\": {\r\n            \"length\": 30,\r\n            \"width\": 20,\r\n            \"height\": 10\r\n        }\r\n    },\r\n    \"references\": {\r\n        \"reference1\": \"Order #12345\",\r\n        \"reference2\": \"Customer ABC\",\r\n        \"reference3\": \"Customer3\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/create-shipping", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["create-shipping"]}}, "response": [{"name": "Create-shipping", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"shipper\": {\r\n        \"companyName\": \"ABC Company\",\r\n        \"personName\": \"<PERSON>\",\r\n        \"phoneNumber\": \"*********\",\r\n        \"cellPhone\": \"971556893100\",\r\n        \"emailAddress\": \"<EMAIL>\",\r\n        \"addressLine1\": \"123 Business Street\",\r\n        \"addressLine2\": \"Suite 100\",\r\n        \"city\": \"Dubai\",\r\n        \"postCode\": \"00000\",\r\n        \"countryCode\": \"AE\"\r\n    },\r\n    \"consignee\": {\r\n        \"companyName\": \"XYZ Corp\",\r\n        \"personName\": \"<PERSON>\",\r\n        \"phoneNumber\": \"*********\",\r\n        \"emailAddress\": \"<EMAIL>\",\r\n        \"addressLine1\": \"456 Delivery Ave\",\r\n        \"city\": \"Abu Dhabi\",\r\n        \"postCode\": \"12345\",\r\n        \"countryCode\": \"AE\"\r\n    },\r\n    \"shipmentDetails\": {\r\n        \"numberOfPieces\": 1,\r\n        \"weight\": 0.5,\r\n        \"dimensions\": {\r\n            \"length\": 30,\r\n            \"width\": 20,\r\n            \"height\": 10\r\n        }\r\n    },\r\n    \"references\": {\r\n        \"reference1\": \"Order #12345\",\r\n        \"reference2\": \"Customer ABC\",\r\n        \"reference3\": \"Customer3\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/create-shipping", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["create-shipping"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "789"}, {"key": "ETag", "value": "W/\"315-qySEPwQhUeXVLQCs27Gk217Yb5A\""}, {"key": "Date", "value": "Fri, 19 Sep 2025 15:07:19 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"Transaction\": null,\n        \"Notifications\": [],\n        \"HasErrors\": false,\n        \"Shipments\": [\n            {\n                \"ID\": \"44263789861\",\n                \"Reference1\": \"Order #12345\",\n                \"Reference2\": \"Customer ABC\",\n                \"Reference3\": \"Customer3\",\n                \"ForeignHAWB\": null,\n                \"HasErrors\": false,\n                \"Notifications\": [],\n                \"ShipmentLabel\": null,\n                \"ShipmentDetails\": {\n                    \"Origin\": \"DXB\",\n                    \"Destination\": \"AUH\",\n                    \"ChargeableWeight\": {\n                        \"Unit\": \"KG\",\n                        \"Value\": 1.2\n                    },\n                    \"DescriptionOfGoods\": \"Items\",\n                    \"GoodsOriginCountry\": \"AE\",\n                    \"NumberOfPieces\": 1,\n                    \"ProductGroup\": \"DOM\",\n                    \"ProductType\": \"ONP\",\n                    \"PaymentType\": \"3\",\n                    \"PaymentOptions\": null,\n                    \"CustomsValueAmount\": null,\n                    \"CashOnDeliveryAmount\": null,\n                    \"InsuranceAmount\": null,\n                    \"CashAdditionalAmount\": null,\n                    \"CollectAmount\": null,\n                    \"Services\": null,\n                    \"OriginCity\": \"Dubai\",\n                    \"DestinationCity\": \"Abu Dhabi\"\n                },\n                \"ShipmentAttachments\": []\n            }\n        ]\n    },\n    \"message\": \"Shipment created successfully\"\n}"}]}, {"name": "Deleviry-Rate", "request": {"auth": {"type": "basic", "basic": [{"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "password", "value": "R123456789$r", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"OriginAddress\": {\r\n        \"Line1\": \"Address line 1\",\r\n        \"Line2\": \"Address line 2\",\r\n        \"Line3\":  \"Address line 3\", \r\n        \"City\": \"Dubai\",\r\n        \"StateOrProvinceCode\": null,\r\n        \"PostCode\": \"131\",\r\n        \"CountryCode\": \"AE\",\r\n        \"Longitude\": 0,\r\n        \"Latitude\": 0,\r\n        \"BuildingNumber\": null,\r\n        \"BuildingName\": null,\r\n        \"Floor\": null,\r\n        \"Apartment\": null,\r\n        \"POBox\": null,\r\n        \"Description\": null\r\n    },\r\n    \"DestinationAddress\": {\r\n       \"Line1\": \"Address line 1\",\r\n        \"Line2\": \"Address line 2\",\r\n        \"Line3\":  \"Address line 3\", \r\n        \"City\": \"Dubai\",\r\n        \"StateOrProvinceCode\": null,\r\n        \"PostCode\": \"131\",\r\n        \"CountryCode\": \"AE\",\r\n        \"Longitude\": 0,\r\n        \"Latitude\": 0,\r\n        \"BuildingNumber\": null,\r\n        \"BuildingName\": null,\r\n        \"Floor\": null,\r\n        \"Apartment\": null,\r\n        \"POBox\": null,\r\n        \"Description\": null\r\n    },\r\n    \"ShipmentDetails\": {\r\n        \"Dimensions\": null,\r\n        \"ActualWeight\": {\r\n            \"Unit\": \"KG\",\r\n            \"Value\": 0.6\r\n        },\r\n        \"ChargeableWeight\": {\r\n            \"Unit\": \"KG\",\r\n            \"Value\": 0.6\r\n        },\r\n        \"DescriptionOfGoods\": null,\r\n        \"GoodsOriginCountry\": null,\r\n        \"NumberOfPieces\": 1,\r\n        \"ProductGroup\": \"DOM\",\r\n        \"ProductType\": \"ONP\",\r\n        \"PaymentType\": \"P\",\r\n        \"PaymentOptions\": null,\r\n        \"CustomsValueAmount\": null,\r\n        \"CashOnDeliveryAmount\": null,\r\n        \"InsuranceAmount\": null,\r\n        \"CashAdditionalAmount\": null,\r\n        \"CashAdditionalAmountDescription\": null,\r\n        \"CollectAmount\": null,\r\n        \"Services\": \"\",\r\n        \"Items\": null,\r\n        \"DeliveryInstructions\": null,\r\n        \"AdditionalProperties\": null,\r\n        \"ContainsDangerousGoods\": false\r\n    },\r\n    \"PreferredCurrencyCode\": \"AED\",\r\n    \"Transaction\": null\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/delivery-rate", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["delivery-rate"]}}, "response": [{"name": "Create-shipping", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"shipper\": {\r\n        \"companyName\": \"ABC Company\",\r\n        \"personName\": \"<PERSON>\",\r\n        \"phoneNumber\": \"*********\",\r\n        \"cellPhone\": \"971556893100\",\r\n        \"emailAddress\": \"<EMAIL>\",\r\n        \"addressLine1\": \"123 Business Street\",\r\n        \"addressLine2\": \"Suite 100\",\r\n        \"city\": \"Dubai\",\r\n        \"postCode\": \"00000\",\r\n        \"countryCode\": \"AE\"\r\n    },\r\n    \"consignee\": {\r\n        \"companyName\": \"XYZ Corp\",\r\n        \"personName\": \"<PERSON>\",\r\n        \"phoneNumber\": \"*********\",\r\n        \"emailAddress\": \"<EMAIL>\",\r\n        \"addressLine1\": \"456 Delivery Ave\",\r\n        \"city\": \"Abu Dhabi\",\r\n        \"postCode\": \"12345\",\r\n        \"countryCode\": \"AE\"\r\n    },\r\n    \"shipmentDetails\": {\r\n        \"numberOfPieces\": 1,\r\n        \"weight\": 0.5,\r\n        \"dimensions\": {\r\n            \"length\": 30,\r\n            \"width\": 20,\r\n            \"height\": 10\r\n        }\r\n    },\r\n    \"references\": {\r\n        \"reference1\": \"Order #12345\",\r\n        \"reference2\": \"Customer ABC\",\r\n        \"reference3\": \"Customer3\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:4000/create-shipping", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["create-shipping"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "X-Powered-By", "value": "Express"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "789"}, {"key": "ETag", "value": "W/\"315-qySEPwQhUeXVLQCs27Gk217Yb5A\""}, {"key": "Date", "value": "Fri, 19 Sep 2025 15:07:19 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"data\": {\n        \"Transaction\": null,\n        \"Notifications\": [],\n        \"HasErrors\": false,\n        \"Shipments\": [\n            {\n                \"ID\": \"44263789861\",\n                \"Reference1\": \"Order #12345\",\n                \"Reference2\": \"Customer ABC\",\n                \"Reference3\": \"Customer3\",\n                \"ForeignHAWB\": null,\n                \"HasErrors\": false,\n                \"Notifications\": [],\n                \"ShipmentLabel\": null,\n                \"ShipmentDetails\": {\n                    \"Origin\": \"DXB\",\n                    \"Destination\": \"AUH\",\n                    \"ChargeableWeight\": {\n                        \"Unit\": \"KG\",\n                        \"Value\": 1.2\n                    },\n                    \"DescriptionOfGoods\": \"Items\",\n                    \"GoodsOriginCountry\": \"AE\",\n                    \"NumberOfPieces\": 1,\n                    \"ProductGroup\": \"DOM\",\n                    \"ProductType\": \"ONP\",\n                    \"PaymentType\": \"3\",\n                    \"PaymentOptions\": null,\n                    \"CustomsValueAmount\": null,\n                    \"CashOnDeliveryAmount\": null,\n                    \"InsuranceAmount\": null,\n                    \"CashAdditionalAmount\": null,\n                    \"CollectAmount\": null,\n                    \"Services\": null,\n                    \"OriginCity\": \"Dubai\",\n                    \"DestinationCity\": \"Abu Dhabi\"\n                },\n                \"ShipmentAttachments\": []\n            }\n        ]\n    },\n    \"message\": \"Shipment created successfully\"\n}"}]}, {"name": "<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:4000/ping", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["ping"]}}, "response": [{"name": "<PERSON>", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:4000/ping", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["ping"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "120"}, {"key": "ETag", "value": "W/\"78-G1eOR4etRPVE2I8GRK5fO84s4kI\""}, {"key": "Date", "value": "Wed, 24 Sep 2025 06:13:45 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"pong\",\n    \"data\": {\n        \"timestamp\": \"2025-09-24T06:13:45.414Z\"\n    },\n    \"timestamp\": \"2025-09-24T06:13:45.414Z\"\n}"}]}, {"name": "health", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:4000/health", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["health"]}}, "response": [{"name": "health", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:4000/health", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["health"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "393"}, {"key": "ETag", "value": "W/\"189-R/+2PzrDF0Cxn3ph2QHV7w1/P1E\""}, {"key": "Date", "value": "Wed, 24 Sep 2025 06:13:54 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Service is healthy\",\n    \"data\": {\n        \"status\": \"healthy\",\n        \"timestamp\": \"2025-09-24T06:13:54.813Z\",\n        \"version\": \"1.0.0\",\n        \"environment\": \"development\",\n        \"uptime\": 1299.5072066,\n        \"memory\": {\n            \"rss\": 289112064,\n            \"heapTotal\": 232611840,\n            \"heapUsed\": 226547504,\n            \"external\": 26254978,\n            \"arrayBuffers\": 22041155\n        },\n        \"services\": {\n            \"database\": \"connected\",\n            \"aramex\": \"configured\"\n        }\n    },\n    \"timestamp\": \"2025-09-24T06:13:54.813Z\"\n}"}]}, {"name": "info", "request": {"method": "GET", "header": [], "url": {"raw": "http://localhost:4000/info", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["info"]}}, "response": [{"name": "info", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "http://localhost:4000/info", "protocol": "http", "host": ["localhost"], "port": "4000", "path": ["info"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "", "header": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Type", "value": "application/json; charset=utf-8"}, {"key": "Content-Length", "value": "564"}, {"key": "ETag", "value": "W/\"234-qKpVHV690nkS1kCfbs1Fg9LqS8o\""}, {"key": "Date", "value": "Wed, 24 Sep 2025 06:14:00 GMT"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Keep-Alive", "value": "timeout=5"}], "cookie": [], "body": "{\n    \"success\": true,\n    \"message\": \"Service information retrieved\",\n    \"data\": {\n        \"name\": \"Delivery Microservice\",\n        \"version\": \"1.0.0\",\n        \"description\": \"Aramex delivery service integration\",\n        \"environment\": \"development\",\n        \"endpoints\": {\n            \"webhook\": \"POST /webhook\",\n            \"health\": \"GET /health\",\n            \"ping\": \"GET /ping\",\n            \"createShipment\": \"POST /create-shipping\",\n            \"calculateRate\": \"POST /delivery-rate\",\n            \"webhookLogs\": \"GET /webhooks/logs\",\n            \"webhookStats\": \"GET /webhooks/stats\"\n        },\n        \"features\": [\n            \"Shipment creation\",\n            \"Rate calculation\",\n            \"Webhook processing\",\n            \"Tracking integration\"\n        ]\n    },\n    \"timestamp\": \"2025-09-24T06:14:00.749Z\"\n}"}]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\r\n\t\"ClientInfo\": {\r\n        \"UserName\": \"{{user}}\",\r\n        \"Password\": \"{{password}}\",\r\n        \"Version\": \"v1.0\",\r\n        \"AccountNumber\": \"{{accountno}}\",\r\n        \"AccountPin\": \"{{accountpin}}\",\r\n        \"AccountEntity\": \"DXB\",\r\n        \"AccountCountryCode\": \"AE\",\r\n        \"Source\": 0,\r\n        \"PreferredLanguageCode\": null\r\n\t},\r\n\t\"Pickup\": {\r\n\t\t\"PickupAddress\": {\r\n\t\t\t\"Line1\": \"Test Address\",\r\n\t\t\t\"Line2\": \"Test Address Line 2\",\r\n\t\t\t\"Line3\": \"\",\r\n\t\t\t\"City\": \"Dubai\",\r\n\t\t\t\"StateOrProvinceCode\": \"Dubai\",\r\n\t\t\t\"PostCode\": \"\",\r\n\t\t\t\"CountryCode\": \"AE\",\r\n\t\t\t\"Longitude\": 0,\r\n\t\t\t\"Latitude\": 0,\r\n\t\t\t\"BuildingNumber\": null,\r\n\t\t\t\"BuildingName\": null,\r\n\t\t\t\"Floor\": null,\r\n\t\t\t\"Apartme nt\": null,\r\n\t\t\t\"POBox\": null,\r\n\t\t\t\"Description\": null\r\n\t\t},\r\n\t\t\"PickupContact\": {\r\n\t\t\t\"Department\": \"Test Department\",\r\n\t\t\t\"PersonName\": \"Test Person Name\",\r\n\t\t\t\"Title\": null,\r\n\t\t\t\"CompanyName\": \"Test Person/Company Name\",\r\n\t\t\t\"PhoneNumber1\": \"***********\",\r\n\t\t\t\"PhoneNumber1Ext\": null,\r\n\t\t\t\"PhoneNumber2\": null,\r\n\t\t\t\"PhoneNumber2Ext\": null,\r\n\t\t\t\"FaxNumber\": null,\r\n\t\t\t\"CellPhone\": \"***********\",\r\n\t\t\t\"EmailAddress\": \"<EMAIL>\",\r\n\t\t\t\"Type\": null\r\n\t\t},\r\n\t\t\"PickupLocation\": \"Reception\",\r\n\t\t\"PickupDate\": \"/Date({{next_day_timestamp}})/\",\r\n\t\t\"ReadyTime\": \"/Date({{ready_time}})/\",\r\n\t\t\"LastPickupTime\": \"/Date({{last_pickup_time}})/\",\r\n\t\t\"ClosingTime\": \"/Date({{closing_time}})/\",\r\n\t\t\"Comments\": \"\",\r\n\t\t\"Reference1\": \"001\",\r\n\t\t\"Reference2\": \"\",\r\n\t\t\"Vehicle\": \"Bike\",\r\n\t\t\"Shipments\": null,\r\n\t\t\"PickupItems\": [{\r\n\t\t\t\"ProductGroup\": \"DOM\",\r\n\t\t\t\"ProductType\": \"ONP\",\r\n\t\t\t\"NumberOfShipments\": 1,\r\n\t\t\t\"PackageType\": \"Box\",\r\n\t\t\t\"Payment\": \"P\",\r\n\t\t\t\"ShipmentWeight\": {\r\n\t\t\t\t\"Unit\": \"KG\",\r\n\t\t\t\t\"Value\": 0.5\r\n\t\t\t},\r\n\t\t\t\"ShipmentVolume\": null,\r\n\t\t\t\"NumberOfPieces\": 1,\r\n\t\t\t\"CashAmount\": null,\r\n\t\t\t\"ExtraCharges\": null,\r\n\t\t\t\"ShipmentDimensions\": {\r\n\t\t\t\t\"Length\": 0,\r\n\t\t\t\t\"Width\": 0,\r\n\t\t\t\t\"Height\": 0,\r\n\t\t\t\t\"Unit\": \"\"\r\n\t\t\t},\r\n\t\t\t\"Comments\": \"Airway Bill Number:44097846262\"\r\n\t\t}],\r\n\t\t\"Status\": \"Ready\",\r\n\t\t\"ExistingShipments\": null,\r\n\t\t\"Branch\": \"\",\r\n\t\t\"RouteCode\": \"\"\r\n\t},\r\n\t\"Transaction\": {\r\n\t\t\"Reference1\": \"\",\r\n\t\t\"Reference2\": \"\",\r\n\t\t\"Reference3\": \"\",\r\n\t\t\"Reference4\": \"\",\r\n\t\t\"Reference5\": \"\"\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://ws.sbx.aramex.net/ShippingAPI.V2/Shipping/Service_1_0.svc/json/CreatePickup", "protocol": "https", "host": ["ws", "sbx", "aramex", "net"], "path": ["ShippingAPI.V2", "Shipping", "Service_1_0.svc", "json", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "response": []}]}